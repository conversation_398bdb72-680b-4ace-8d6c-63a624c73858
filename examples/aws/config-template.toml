[[stack]]
name = "${client_name_lower}_pangolin-setup"
[stack.config]
server = "server-${client_name_lower}"
repo = "${github_repo}"
reclone = true
file_paths = ["docker-compose-setup.yml"]
environment = """
DOMAIN=${domain}
EMAIL=${admin_email}
ADMIN_USERNAME=${admin_username}
ADMIN_PASSWORD=${admin_password}
ADMIN_SUBDOMAIN=${admin_subdomain}
CROWDSEC_ENROLLMENT_KEY=${crowdsec_enrollment_key}
POSTGRES_USER=${postgres_user}
POSTGRES_PASSWORD=${postgres_password}
POSTGRES_HOST=${postgres_host}
STATIC_PAGE_DOMAIN=${static_page_domain}
CLIENT_ID=${oauth_client_id}
CLIENT_SECRET=${oauth_client_secret}
"""

[[stack]]
name = "${client_name_lower}_pangolin-stack"
[stack.config]
server = "server-${client_name_lower}"
files_on_host = true
reclone = true
run_directory = "/etc/komodo/stacks/${client_name_lower}_pangolin-setup"
post_deploy.command = """
  # Add multiple commands on new lines. Supports comments.
  chmod +x initialize_postgres.sh
  ./initialize_postgres.sh
"""

[[procedure]]
name = "${client_name}_ProcedureApply"
description = "This procedure runs the initial setup that write out a compose file for the main stack deployment"

[[procedure.config.stage]]
name = "${client_name}_Setup"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "${client_name_lower}_pangolin-setup", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "Wait For Compose Write"
enabled = true
executions = [
  { execution.type = "Sleep", execution.params.duration_ms = 10000, enabled = true }
]

[[procedure.config.stage]]
name = "${client_name}_Stack"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "${client_name_lower}_pangolin-stack", execution.params.services = [], enabled = true }
]

[[procedure]]
name = "${client_name}_ProcedureDestroy"

[[procedure.config.stage]]
name = "${client_name}_Stack"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "${client_name_lower}_pangolin-stack", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]

[[procedure.config.stage]]
name = "${client_name}_Setup"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "${client_name_lower}_pangolin-setup", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]

[[user_group]]
name = "${client_name}_user_group"
permissions = [
  { target.type = "Server", target.id = "server-${client_name_lower}", level = "Write", specific = ["Attach", "Inspect", "Logs", "Processes", "Terminal"] },
  { target.type = "Stack", target.id = "${client_name_lower}_pangolin-setup", level = "Write", specific = ["Inspect", "Logs", "Terminal"] },
  { target.type = "Stack", target.id = "${client_name_lower}_pangolin-stack", level = "Write", specific = ["Inspect", "Logs", "Terminal"] }
]